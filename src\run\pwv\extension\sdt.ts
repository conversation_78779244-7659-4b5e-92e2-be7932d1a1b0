import actions from "./core/actions";
import apiHandler from "./handlers/apiHandler";
import h from "@/app/helpers/all";
import icons from "./core/icons";
import primitives from "./core/primitives";
import riverstarSdt from "@/app/organizations/riverstar/sdt";
import scheduleHandler from "./handlers/scheduleHandler";
import setup from "./core/setup";
import stepperHandler from "./handlers/stepperHandler";
import taskHandler from "./handlers/taskHandler";

export default {
  setup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, icons),
  actions: h.mergeObjects(riverstarSdt.actions, actions),
  primitives: h.mergeObjects(riverstarSdt.primitives, primitives),
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, apiHandler),
  taskH<PERSON><PERSON>,
  stepperHand<PERSON>,
  scheduleHandler,
} as const;
